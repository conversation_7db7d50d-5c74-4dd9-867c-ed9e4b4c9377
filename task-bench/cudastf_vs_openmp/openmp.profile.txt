WARNING: Device-side CUDA Event completion trace is currently enabled.
         This may increase runtime overhead and the likelihood of false
         dependencies across CUDA Streams. If you wish to avoid this, please
         disable the feature with --cuda-event-trace=false.
graph id 0, M = 10, N = 32, data 0x4426a0, nb_fields 10
Running Task Benchmark
  Configuration:
    Task Graph 1:
      Time Steps: 10
      Max Width: 32
      Dependence Type: stencil_1d
      Radix: 3
      Period: 0
      Fraction Connected: 0.250000
      Kernel:
        Type: compute_bound
        Iterations: 0
        Samples: 16
        Imbalance: 0.000000
      Output Bytes: 16
      Scratch Bytes: 0
Total Tasks 320
Total Dependencies 846
  Unable to estimate local/nonlocal dependencies
Total FLOPs 20480
Total Bytes 0
Elapsed Time 8.893013e-05 seconds
FLOP/s 2.302932e+08
B/s 0.000000e+00
Transfer (estimated):
  Unable to estimate local/nonlocal transfer
Collecting data...
Generating '/tmp/nsys-report-e4ad.qdstrm'

[1/8] [0%                          ] openmp_profile.nsys-rep
[1/8] [0%                          ] openmp_profile.nsys-rep
[1/8] [7%                          ] openmp_profile.nsys-rep
[1/8] [8%                          ] openmp_profile.nsys-rep
[1/8] [9%                          ] openmp_profile.nsys-rep
[1/8] [==========49%               ] openmp_profile.nsys-rep
[1/8] [=====================89%    ] openmp_profile.nsys-rep
[1/8] [======================90%   ] openmp_profile.nsys-rep
[1/8] [======================91%   ] openmp_profile.nsys-rep
[1/8] [======================92%   ] openmp_profile.nsys-rep
[1/8] [========================97% ] openmp_profile.nsys-rep
[1/8] [========================100%] openmp_profile.nsys-rep
[1/8] [========================100%] openmp_profile.nsys-rep

[2/8] [0%                          ] openmp_profile.sqlite
[2/8] [1%                          ] openmp_profile.sqlite
[2/8] [2%                          ] openmp_profile.sqlite
[2/8] [3%                          ] openmp_profile.sqlite
[2/8] [4%                          ] openmp_profile.sqlite
[2/8] [5%                          ] openmp_profile.sqlite
[2/8] [6%                          ] openmp_profile.sqlite
[2/8] [7%                          ] openmp_profile.sqlite
[2/8] [8%                          ] openmp_profile.sqlite
[2/8] [9%                          ] openmp_profile.sqlite
[2/8] [10%                         ] openmp_profile.sqlite
[2/8] [11%                         ] openmp_profile.sqlite
[2/8] [12%                         ] openmp_profile.sqlite
[2/8] [13%                         ] openmp_profile.sqlite
[2/8] [14%                         ] openmp_profile.sqlite
[2/8] [=15%                        ] openmp_profile.sqlite
[2/8] [=16%                        ] openmp_profile.sqlite
[2/8] [=17%                        ] openmp_profile.sqlite
[2/8] [==18%                       ] openmp_profile.sqlite
[2/8] [==19%                       ] openmp_profile.sqlite
[2/8] [==20%                       ] openmp_profile.sqlite
[2/8] [==21%                       ] openmp_profile.sqlite
[2/8] [===22%                      ] openmp_profile.sqlite
[2/8] [===23%                      ] openmp_profile.sqlite
[2/8] [===24%                      ] openmp_profile.sqlite
[2/8] [====25%                     ] openmp_profile.sqlite
[2/8] [====26%                     ] openmp_profile.sqlite
[2/8] [====27%                     ] openmp_profile.sqlite
[2/8] [====28%                     ] openmp_profile.sqlite
[2/8] [=====29%                    ] openmp_profile.sqlite
[2/8] [=====30%                    ] openmp_profile.sqlite
[2/8] [=====31%                    ] openmp_profile.sqlite
[2/8] [=====32%                    ] openmp_profile.sqlite
[2/8] [======33%                   ] openmp_profile.sqlite
[2/8] [======34%                   ] openmp_profile.sqlite
[2/8] [======35%                   ] openmp_profile.sqlite
[2/8] [=======36%                  ] openmp_profile.sqlite
[2/8] [=======37%                  ] openmp_profile.sqlite
[2/8] [=======38%                  ] openmp_profile.sqlite
[2/8] [=======39%                  ] openmp_profile.sqlite
[2/8] [========40%                 ] openmp_profile.sqlite
[2/8] [========41%                 ] openmp_profile.sqlite
[2/8] [========42%                 ] openmp_profile.sqlite
[2/8] [=========43%                ] openmp_profile.sqlite
[2/8] [=========44%                ] openmp_profile.sqlite
[2/8] [=========45%                ] openmp_profile.sqlite
[2/8] [=========46%                ] openmp_profile.sqlite
[2/8] [==========47%               ] openmp_profile.sqlite
[2/8] [==========48%               ] openmp_profile.sqlite
[2/8] [==========49%               ] openmp_profile.sqlite
[2/8] [===========50%              ] openmp_profile.sqlite
[2/8] [===========51%              ] openmp_profile.sqlite
[2/8] [===========52%              ] openmp_profile.sqlite
[2/8] [===========53%              ] openmp_profile.sqlite
[2/8] [============54%             ] openmp_profile.sqlite
[2/8] [============55%             ] openmp_profile.sqlite
[2/8] [============56%             ] openmp_profile.sqlite
[2/8] [============57%             ] openmp_profile.sqlite
[2/8] [=============58%            ] openmp_profile.sqlite
[2/8] [=============59%            ] openmp_profile.sqlite
[2/8] [=============60%            ] openmp_profile.sqlite
[2/8] [==============61%           ] openmp_profile.sqlite
[2/8] [==============62%           ] openmp_profile.sqlite
[2/8] [==============63%           ] openmp_profile.sqlite
[2/8] [==============64%           ] openmp_profile.sqlite
[2/8] [===============65%          ] openmp_profile.sqlite
[2/8] [===============66%          ] openmp_profile.sqlite
[2/8] [===============67%          ] openmp_profile.sqlite
[2/8] [================68%         ] openmp_profile.sqlite
[2/8] [================69%         ] openmp_profile.sqlite
[2/8] [================70%         ] openmp_profile.sqlite
[2/8] [================71%         ] openmp_profile.sqlite
[2/8] [=================72%        ] openmp_profile.sqlite
[2/8] [=================73%        ] openmp_profile.sqlite
[2/8] [=================74%        ] openmp_profile.sqlite
[2/8] [==================75%       ] openmp_profile.sqlite
[2/8] [==================76%       ] openmp_profile.sqlite
[2/8] [==================77%       ] openmp_profile.sqlite
[2/8] [==================78%       ] openmp_profile.sqlite
[2/8] [===================79%      ] openmp_profile.sqlite
[2/8] [===================80%      ] openmp_profile.sqlite
[2/8] [===================81%      ] openmp_profile.sqlite
[2/8] [===================82%      ] openmp_profile.sqlite
[2/8] [====================83%     ] openmp_profile.sqlite
[2/8] [====================84%     ] openmp_profile.sqlite
[2/8] [====================85%     ] openmp_profile.sqlite
[2/8] [=====================86%    ] openmp_profile.sqlite
[2/8] [=====================87%    ] openmp_profile.sqlite
[2/8] [=====================88%    ] openmp_profile.sqlite
[2/8] [=====================89%    ] openmp_profile.sqlite
[2/8] [======================90%   ] openmp_profile.sqlite
[2/8] [======================91%   ] openmp_profile.sqlite
[2/8] [======================92%   ] openmp_profile.sqlite
[2/8] [=======================93%  ] openmp_profile.sqlite
[2/8] [=======================94%  ] openmp_profile.sqlite
[2/8] [=======================95%  ] openmp_profile.sqlite
[2/8] [=======================96%  ] openmp_profile.sqlite
[2/8] [========================97% ] openmp_profile.sqlite
[2/8] [========================98% ] openmp_profile.sqlite
[2/8] [========================99% ] openmp_profile.sqlite
[2/8] [========================100%] openmp_profile.sqlite
[2/8] [========================100%] openmp_profile.sqlite
SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/openmp_profile.sqlite does not contain NV Tools Extension (NVTX) data.
SKIPPED: No data available.
SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/openmp_profile.sqlite does not contain CUDA trace data.
SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/openmp_profile.sqlite does not contain CUDA kernel data.
SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/openmp_profile.sqlite does not contain GPU memory data.
SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/openmp_profile.sqlite does not contain GPU memory data.
[3/8] Executing 'nvtx_sum' stats report
[4/8] Executing 'osrt_sum' stats report
[5/8] Executing 'cuda_api_sum' stats report
[6/8] Executing 'cuda_gpu_kern_sum' stats report
[7/8] Executing 'cuda_gpu_mem_time_sum' stats report
[8/8] Executing 'cuda_gpu_mem_size_sum' stats report
Generated:
	/root/stf_exp/task-bench/cudastf_vs_openmp/openmp_profile.nsys-rep
	/root/stf_exp/task-bench/cudastf_vs_openmp/openmp_profile.sqlite
