#!/bin/bash

# Quick test script for CUDASTF vs OpenMP
# Runs a minimal set of tests for validation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Quick CUDASTF vs OpenMP Test ===${NC}"

# Check if we're in the right directory
if [[ ! -d "scripts" ]] || [[ ! -d "analysis" ]]; then
    echo -e "${RED}Error: Please run this script from the cudastf_vs_openmp directory${NC}"
    exit 1
fi

# Test configurations
TASK_WIDTH=4
STEPS=5

echo "Testing with width=$TASK_WIDTH, steps=$STEPS"
echo

# Test CUDASTF
echo -e "${YELLOW}Testing CUDASTF...${NC}"
if [[ -f "../cudastf/main" ]]; then
    echo "Running: ../cudastf/main -steps $STEPS -width $TASK_WIDTH -type trivial"
    if timeout 30 ../cudastf/main -steps $STEPS -width $TASK_WIDTH -type trivial; then
        echo -e "${GREEN}✓ CUDASTF trivial test passed${NC}"
    else
        echo -e "${RED}✗ CUDASTF trivial test failed${NC}"
    fi
    
    echo "Running: ../cudastf/main -steps $STEPS -width $TASK_WIDTH -type stencil_1d -kernel compute_bound -iter 100"
    if timeout 30 ../cudastf/main -steps $STEPS -width $TASK_WIDTH -type stencil_1d -kernel compute_bound -iter 100; then
        echo -e "${GREEN}✓ CUDASTF stencil_1d test passed${NC}"
    else
        echo -e "${RED}✗ CUDASTF stencil_1d test failed${NC}"
    fi
else
    echo -e "${RED}✗ CUDASTF binary not found. Run 'make' in ../cudastf first.${NC}"
fi

echo

# Test OpenMP
echo -e "${YELLOW}Testing OpenMP...${NC}"
if [[ -f "../openmp/main" ]]; then
    export USE_OPENMP=1
    export LD_LIBRARY_PATH=/usr/local/clang/lib:$LD_LIBRARY_PATH
    
    echo "Running: ../openmp/main -steps $STEPS -width $TASK_WIDTH -type trivial -worker 2"
    if timeout 30 ../openmp/main -steps $STEPS -width $TASK_WIDTH -type trivial -worker 2; then
        echo -e "${GREEN}✓ OpenMP trivial test passed${NC}"
    else
        echo -e "${RED}✗ OpenMP trivial test failed${NC}"
    fi
    
    echo "Running: ../openmp/main -steps $STEPS -width $TASK_WIDTH -type stencil_1d -kernel compute_bound -iter 100 -worker 2"
    if timeout 30 ../openmp/main -steps $STEPS -width $TASK_WIDTH -type stencil_1d -kernel compute_bound -iter 100 -worker 2; then
        echo -e "${GREEN}✓ OpenMP stencil_1d test passed${NC}"
    else
        echo -e "${RED}✗ OpenMP stencil_1d test failed${NC}"
    fi
else
    echo -e "${RED}✗ OpenMP binary not found. Run 'make' in ../openmp first.${NC}"
fi

echo
echo -e "${BLUE}Quick test completed!${NC}"
echo
echo "To run full benchmarks:"
echo "  ./run_complete_analysis.sh --quick    # Quick benchmark"
echo "  ./run_complete_analysis.sh            # Full benchmark"
echo "  ./run_complete_analysis.sh -w 8 -r 3  # Custom width and runs"
