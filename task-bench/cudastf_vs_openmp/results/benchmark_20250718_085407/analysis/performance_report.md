# CUDASTF vs OpenMP Performance Analysis Report

Generated on: 2025-07-18 09:13:51.477191

## Executive Summary

- Total successful benchmark runs: 4200
- Implementations tested: openmp, cudastf
- Task types tested: 10
- Kernel variants tested: 5

## Overall Performance

- OpenMP average wall time: 0.011710 seconds
- CUDASTF average wall time: 0.374619 seconds
- Overall speedup factor: 0.03x

❌ CUDASTF shows performance regression

## Performance Analysis by Scenario

### Best CUDASTF Performance (Top 5 Speedups)

1. fft + default: 0.04x speedup
2. random_nearest + load_imbalance: 0.03x speedup
3. random_nearest + compute_bound: 0.03x speedup
4. nearest + load_imbalance: 0.03x speedup
5. spread -period 2 + compute_bound: 0.03x speedup

### Worst CUDASTF Performance (Bottom 5 Speedups)

1. spread -period 2 + load_imbalance: 0.03x speedup
2. stencil_1d + memory_bound: 0.03x speedup
3. trivial + load_imbalance: 0.03x speedup
4. dom + default: 0.03x speedup
5. fft + memory_bound: 0.03x speedup

## Recommendations

Based on the benchmark results, consider the following improvements:

1. **Performance Bottlenecks**: Focus on scenarios with speedup < 1.0
2. **Memory Management**: Analyze memory-bound kernel performance
3. **Task Scheduling**: Optimize task graph construction and execution
4. **GPU Utilization**: Ensure efficient GPU resource utilization
5. **Communication Patterns**: Optimize data transfer patterns

