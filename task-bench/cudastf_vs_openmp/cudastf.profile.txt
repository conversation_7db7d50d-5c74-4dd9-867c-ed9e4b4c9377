WARNING: Device-side CUDA Event completion trace is currently enabled.
         This may increase runtime overhead and the likelihood of false
         dependencies across CUDA Streams. If you wish to avoid this, please
         disable the feature with --cuda-event-trace=false.
Running Task Benchmark
  Configuration:
    Task Graph 1:
      Time Steps: 10
      Max Width: 32
      Dependence Type: stencil_1d
      Radix: 3
      Period: 0
      Fraction Connected: 0.250000
      Kernel:
        Type: compute_bound
        Iterations: 0
        Samples: 16
        Imbalance: 0.000000
      Output Bytes: 16
      Scratch Bytes: 0
Total Tasks 320
Total Dependencies 846
  Unable to estimate local/nonlocal dependencies
Total FLOPs 20480
Total Bytes 0
Elapsed Time 1.490498e-02 seconds
FLOP/s 1.374038e+06
B/s 0.000000e+00
Transfer (estimated):
  Unable to estimate local/nonlocal transfer
Collecting data...
Generating '/tmp/nsys-report-1659.qdstrm'

[1/8] [0%                          ] cudastf_profile.nsys-rep
[1/8] [0%                          ] cudastf_profile.nsys-rep
[1/8] [7%                          ] cudastf_profile.nsys-rep
[1/8] [12%                         ] cudastf_profile.nsys-rep
[1/8] [=16%                        ] cudastf_profile.nsys-rep
[1/8] [=17%                        ] cudastf_profile.nsys-rep
[1/8] [==18%                       ] cudastf_profile.nsys-rep
[1/8] [===23%                      ] cudastf_profile.nsys-rep
[1/8] [=====30%                    ] cudastf_profile.nsys-rep
[1/8] [=================74%        ] cudastf_profile.nsys-rep
[1/8] [==================75%       ] cudastf_profile.nsys-rep
[1/8] [========================100%] cudastf_profile.nsys-rep
[1/8] [========================100%] cudastf_profile.nsys-rep

[2/8] [0%                          ] cudastf_profile.sqlite
[2/8] [1%                          ] cudastf_profile.sqlite
[2/8] [2%                          ] cudastf_profile.sqlite
[2/8] [3%                          ] cudastf_profile.sqlite
[2/8] [4%                          ] cudastf_profile.sqlite
[2/8] [5%                          ] cudastf_profile.sqlite
[2/8] [6%                          ] cudastf_profile.sqlite
[2/8] [7%                          ] cudastf_profile.sqlite
[2/8] [8%                          ] cudastf_profile.sqlite
[2/8] [9%                          ] cudastf_profile.sqlite
[2/8] [10%                         ] cudastf_profile.sqlite
[2/8] [11%                         ] cudastf_profile.sqlite
[2/8] [12%                         ] cudastf_profile.sqlite
[2/8] [13%                         ] cudastf_profile.sqlite
[2/8] [14%                         ] cudastf_profile.sqlite
[2/8] [=15%                        ] cudastf_profile.sqlite
[2/8] [=16%                        ] cudastf_profile.sqlite
[2/8] [=17%                        ] cudastf_profile.sqlite
[2/8] [==18%                       ] cudastf_profile.sqlite
[2/8] [==19%                       ] cudastf_profile.sqlite
[2/8] [==20%                       ] cudastf_profile.sqlite
[2/8] [==21%                       ] cudastf_profile.sqlite
[2/8] [===22%                      ] cudastf_profile.sqlite
[2/8] [===23%                      ] cudastf_profile.sqlite
[2/8] [===24%                      ] cudastf_profile.sqlite
[2/8] [====25%                     ] cudastf_profile.sqlite
[2/8] [====26%                     ] cudastf_profile.sqlite
[2/8] [====27%                     ] cudastf_profile.sqlite
[2/8] [====28%                     ] cudastf_profile.sqlite
[2/8] [=====29%                    ] cudastf_profile.sqlite
[2/8] [=====30%                    ] cudastf_profile.sqlite
[2/8] [=====31%                    ] cudastf_profile.sqlite
[2/8] [=====32%                    ] cudastf_profile.sqlite
[2/8] [======33%                   ] cudastf_profile.sqlite
[2/8] [======34%                   ] cudastf_profile.sqlite
[2/8] [======35%                   ] cudastf_profile.sqlite
[2/8] [=======36%                  ] cudastf_profile.sqlite
[2/8] [=======37%                  ] cudastf_profile.sqlite
[2/8] [=======38%                  ] cudastf_profile.sqlite
[2/8] [=======39%                  ] cudastf_profile.sqlite
[2/8] [========40%                 ] cudastf_profile.sqlite
[2/8] [========41%                 ] cudastf_profile.sqlite
[2/8] [========42%                 ] cudastf_profile.sqlite
[2/8] [=========43%                ] cudastf_profile.sqlite
[2/8] [=========44%                ] cudastf_profile.sqlite
[2/8] [=========45%                ] cudastf_profile.sqlite
[2/8] [=========46%                ] cudastf_profile.sqlite
[2/8] [==========47%               ] cudastf_profile.sqlite
[2/8] [==========48%               ] cudastf_profile.sqlite
[2/8] [==========49%               ] cudastf_profile.sqlite
[2/8] [===========50%              ] cudastf_profile.sqlite
[2/8] [===========51%              ] cudastf_profile.sqlite
[2/8] [===========52%              ] cudastf_profile.sqlite
[2/8] [===========53%              ] cudastf_profile.sqlite
[2/8] [============54%             ] cudastf_profile.sqlite
[2/8] [============55%             ] cudastf_profile.sqlite
[2/8] [============56%             ] cudastf_profile.sqlite
[2/8] [============57%             ] cudastf_profile.sqlite
[2/8] [=============58%            ] cudastf_profile.sqlite
[2/8] [=============59%            ] cudastf_profile.sqlite
[2/8] [=============60%            ] cudastf_profile.sqlite
[2/8] [==============61%           ] cudastf_profile.sqlite
[2/8] [==============62%           ] cudastf_profile.sqlite
[2/8] [==============63%           ] cudastf_profile.sqlite
[2/8] [==============64%           ] cudastf_profile.sqlite
[2/8] [===============65%          ] cudastf_profile.sqlite
[2/8] [===============66%          ] cudastf_profile.sqlite
[2/8] [===============67%          ] cudastf_profile.sqlite
[2/8] [================68%         ] cudastf_profile.sqlite
[2/8] [================69%         ] cudastf_profile.sqlite
[2/8] [================70%         ] cudastf_profile.sqlite
[2/8] [================71%         ] cudastf_profile.sqlite
[2/8] [=================72%        ] cudastf_profile.sqlite
[2/8] [=================73%        ] cudastf_profile.sqlite
[2/8] [=================74%        ] cudastf_profile.sqlite
[2/8] [==================75%       ] cudastf_profile.sqlite
[2/8] [==================76%       ] cudastf_profile.sqlite
[2/8] [==================77%       ] cudastf_profile.sqlite
[2/8] [==================78%       ] cudastf_profile.sqlite
[2/8] [===================79%      ] cudastf_profile.sqlite
[2/8] [===================80%      ] cudastf_profile.sqlite
[2/8] [===================81%      ] cudastf_profile.sqlite
[2/8] [===================82%      ] cudastf_profile.sqlite
[2/8] [====================83%     ] cudastf_profile.sqlite
[2/8] [====================84%     ] cudastf_profile.sqlite
[2/8] [====================85%     ] cudastf_profile.sqlite
[2/8] [=====================86%    ] cudastf_profile.sqlite
[2/8] [=====================87%    ] cudastf_profile.sqlite
[2/8] [=====================88%    ] cudastf_profile.sqlite
[2/8] [=====================89%    ] cudastf_profile.sqlite
[2/8] [======================90%   ] cudastf_profile.sqlite
[2/8] [======================91%   ] cudastf_profile.sqlite
[2/8] [======================92%   ] cudastf_profile.sqlite
[2/8] [=======================93%  ] cudastf_profile.sqlite
[2/8] [=======================94%  ] cudastf_profile.sqlite
[2/8] [=======================95%  ] cudastf_profile.sqlite
[2/8] [=======================96%  ] cudastf_profile.sqlite
[2/8] [========================97% ] cudastf_profile.sqlite
[2/8] [========================98% ] cudastf_profile.sqlite
[2/8] [========================99% ] cudastf_profile.sqlite
[2/8] [========================100%] cudastf_profile.sqlite
[2/8] [========================100%] cudastf_profile.sqlite
[3/8] Executing 'nvtx_sum' stats report

 Time (%)  Total Time (ns)  Instances  Avg (ns)  Med (ns)  Min (ns)  Max (ns)  StdDev (ns)   Style      Range   
 --------  ---------------  ---------  --------  --------  --------  --------  -----------  -------  -----------
      3.3            96896          1   96896.0   96896.0     96896     96896          0.0  PushPop  :task(0,0) 
      0.6            18448          1   18448.0   18448.0     18448     18448          0.0  PushPop  :task(29,7)
      0.6            17897          1   17897.0   17897.0     17897     17897          0.0  PushPop  :task(0,9) 
      0.6            16853          1   16853.0   16853.0     16853     16853          0.0  PushPop  :task(0,1) 
      0.5            14902          1   14902.0   14902.0     14902     14902          0.0  PushPop  :task(10,9)
      0.5            14397          1   14397.0   14397.0     14397     14397          0.0  PushPop  :task(25,1)
      0.5            14328          1   14328.0   14328.0     14328     14328          0.0  PushPop  :task(18,5)
      0.5            13951          1   13951.0   13951.0     13951     13951          0.0  PushPop  :task(29,3)
      0.5            13608          1   13608.0   13608.0     13608     13608          0.0  PushPop  :task(2,0) 
      0.5            13272          1   13272.0   13272.0     13272     13272          0.0  PushPop  :task(15,7)
      0.4            13120          1   13120.0   13120.0     13120     13120          0.0  PushPop  :task(7,6) 
      0.4            13042          1   13042.0   13042.0     13042     13042          0.0  PushPop  :task(22,3)
      0.4            12746          1   12746.0   12746.0     12746     12746          0.0  PushPop  :task(5,6) 
      0.4            12569          1   12569.0   12569.0     12569     12569          0.0  PushPop  :task(26,1)
      0.4            12129          1   12129.0   12129.0     12129     12129          0.0  PushPop  :task(24,5)
      0.4            12078          1   12078.0   12078.0     12078     12078          0.0  PushPop  :task(1,0) 
      0.4            11589          1   11589.0   11589.0     11589     11589          0.0  PushPop  :task(10,4)
      0.4            11571          1   11571.0   11571.0     11571     11571          0.0  PushPop  :task(1,3) 
      0.4            11463          1   11463.0   11463.0     11463     11463          0.0  PushPop  :task(7,2) 
      0.4            11433          1   11433.0   11433.0     11433     11433          0.0  PushPop  :task(17,8)
      0.4            11344          1   11344.0   11344.0     11344     11344          0.0  PushPop  :task(13,6)
      0.4            11322          1   11322.0   11322.0     11322     11322          0.0  PushPop  :task(22,9)
      0.4            11222          1   11222.0   11222.0     11222     11222          0.0  PushPop  :task(1,1) 
      0.4            11188          1   11188.0   11188.0     11188     11188          0.0  PushPop  :task(9,3) 
      0.4            11128          1   11128.0   11128.0     11128     11128          0.0  PushPop  :task(15,1)
      0.4            10952          1   10952.0   10952.0     10952     10952          0.0  PushPop  :task(11,9)
      0.4            10922          1   10922.0   10922.0     10922     10922          0.0  PushPop  :task(18,2)
      0.4            10818          1   10818.0   10818.0     10818     10818          0.0  PushPop  :task(3,6) 
      0.4            10777          1   10777.0   10777.0     10777     10777          0.0  PushPop  :task(16,7)
      0.4            10736          1   10736.0   10736.0     10736     10736          0.0  PushPop  :task(11,1)
      0.4            10678          1   10678.0   10678.0     10678     10678          0.0  PushPop  :task(17,1)
      0.4            10675          1   10675.0   10675.0     10675     10675          0.0  PushPop  :task(28,1)
      0.4            10658          1   10658.0   10658.0     10658     10658          0.0  PushPop  :task(21,4)
      0.4            10644          1   10644.0   10644.0     10644     10644          0.0  PushPop  :task(5,2) 
      0.4            10612          1   10612.0   10612.0     10612     10612          0.0  PushPop  :task(31,3)
      0.4            10610          1   10610.0   10610.0     10610     10610          0.0  PushPop  :task(7,7) 
      0.4            10608          1   10608.0   10608.0     10608     10608          0.0  PushPop  :task(3,0) 
      0.4            10599          1   10599.0   10599.0     10599     10599          0.0  PushPop  :task(12,4)
      0.4            10561          1   10561.0   10561.0     10561     10561          0.0  PushPop  :task(13,1)
      0.4            10540          1   10540.0   10540.0     10540     10540          0.0  PushPop  :task(19,8)
      0.4            10497          1   10497.0   10497.0     10497     10497          0.0  PushPop  :task(26,5)
      0.4            10495          1   10495.0   10495.0     10495     10495          0.0  PushPop  :task(18,3)
      0.4            10472          1   10472.0   10472.0     10472     10472          0.0  PushPop  :task(13,5)
      0.4            10458          1   10458.0   10458.0     10458     10458          0.0  PushPop  :task(18,7)
      0.4            10458          1   10458.0   10458.0     10458     10458          0.0  PushPop  :task(8,4) 
      0.4            10453          1   10453.0   10453.0     10453     10453          0.0  PushPop  :task(14,7)
      0.4            10445          1   10445.0   10445.0     10445     10445          0.0  PushPop  :task(3,7) 
      0.4            10424          1   10424.0   10424.0     10424     10424          0.0  PushPop  :task(20,2)
      0.4            10424          1   10424.0   10424.0     10424     10424          0.0  PushPop  :task(5,7) 
      0.4            10423          1   10423.0   10423.0     10423     10423          0.0  PushPop  :task(6,8) 
      0.4            10372          1   10372.0   10372.0     10372     10372          0.0  PushPop  :task(8,8) 
      0.4            10347          1   10347.0   10347.0     10347     10347          0.0  PushPop  :task(11,5)
      0.4            10331          1   10331.0   10331.0     10331     10331          0.0  PushPop  :task(15,6)
      0.4            10329          1   10329.0   10329.0     10329     10329          0.0  PushPop  :task(30,8)
      0.4            10317          1   10317.0   10317.0     10317     10317          0.0  PushPop  :task(24,9)
      0.4            10314          1   10314.0   10314.0     10314     10314          0.0  PushPop  :task(27,7)
      0.3            10300          1   10300.0   10300.0     10300     10300          0.0  PushPop  :task(23,4)
      0.3            10294          1   10294.0   10294.0     10294     10294          0.0  PushPop  :task(24,6)
      0.3            10282          1   10282.0   10282.0     10282     10282          0.0  PushPop  :task(1,4) 
      0.3            10279          1   10279.0   10279.0     10279     10279          0.0  PushPop  :task(30,1)
      0.3            10268          1   10268.0   10268.0     10268     10268          0.0  PushPop  :task(9,9) 
      0.3            10251          1   10251.0   10251.0     10251     10251          0.0  PushPop  :task(15,5)
      0.3            10233          1   10233.0   10233.0     10233     10233          0.0  PushPop  :task(9,2) 
      0.3            10219          1   10219.0   10219.0     10219     10219          0.0  PushPop  :task(20,3)
      0.3            10208          1   10208.0   10208.0     10208     10208          0.0  PushPop  :task(7,3) 
      0.3            10201          1   10201.0   10201.0     10201     10201          0.0  PushPop  :task(2,5) 
      0.3            10184          1   10184.0   10184.0     10184     10184          0.0  PushPop  :task(20,9)
      0.3            10165          1   10165.0   10165.0     10165     10165          0.0  PushPop  :task(29,2)
      0.3            10160          1   10160.0   10160.0     10160     10160          0.0  PushPop  :task(21,8)
      0.3            10085          1   10085.0   10085.0     10085     10085          0.0  PushPop  :task(26,6)
      0.3            10040          1   10040.0   10040.0     10040     10040          0.0  PushPop  :task(31,2)
      0.3            10031          1   10031.0   10031.0     10031     10031          0.0  PushPop  :task(10,8)
      0.3             9728          1    9728.0    9728.0      9728      9728          0.0  PushPop  :task(25,0)
      0.3             9669          1    9669.0    9669.0      9669      9669          0.0  PushPop  :task(3,1) 
      0.3             9622          1    9622.0    9622.0      9622      9622          0.0  PushPop  :task(8,6) 
      0.3             9482          1    9482.0    9482.0      9482      9482          0.0  PushPop  :task(21,7)
      0.3             9454          1    9454.0    9454.0      9454      9454          0.0  PushPop  :task(2,1) 
      0.3             9416          1    9416.0    9416.0      9416      9416          0.0  PushPop  :task(17,4)
      0.3             9413          1    9413.0    9413.0      9413      9413          0.0  PushPop  :task(4,8) 
      0.3             9303          1    9303.0    9303.0      9303      9303          0.0  PushPop  :task(2,8) 
      0.3             9299          1    9299.0    9299.0      9299      9299          0.0  PushPop  :task(5,1) 
      0.3             9251          1    9251.0    9251.0      9251      9251          0.0  PushPop  :task(24,1)
      0.3             9234          1    9234.0    9234.0      9234      9234          0.0  PushPop  :task(29,1)
      0.3             9174          1    9174.0    9174.0      9174      9174          0.0  PushPop  :task(22,1)
      0.3             9126          1    9126.0    9126.0      9126      9126          0.0  PushPop  :task(22,5)
      0.3             9094          1    9094.0    9094.0      9094      9094          0.0  PushPop  :task(9,5) 
      0.3             9093          1    9093.0    9093.0      9093      9093          0.0  PushPop  :task(14,1)
      0.3             9088          1    9088.0    9088.0      9088      9088          0.0  PushPop  :task(3,3) 
      0.3             9072          1    9072.0    9072.0      9072      9072          0.0  PushPop  :task(7,1) 
      0.3             9065          1    9065.0    9065.0      9065      9065          0.0  PushPop  :task(5,9) 
      0.3             9037          1    9037.0    9037.0      9037      9037          0.0  PushPop  :task(9,1) 
      0.3             9026          1    9026.0    9026.0      9026      9026          0.0  PushPop  :task(9,6) 
      0.3             9007          1    9007.0    9007.0      9007      9007          0.0  PushPop  :task(4,1) 
      0.3             9004          1    9004.0    9004.0      9004      9004          0.0  PushPop  :task(5,3) 
      0.3             8990          1    8990.0    8990.0      8990      8990          0.0  PushPop  :task(6,4) 
      0.3             8970          1    8970.0    8970.0      8970      8970          0.0  PushPop  :task(23,7)
      0.3             8968          1    8968.0    8968.0      8968      8968          0.0  PushPop  :task(25,3)
      0.3             8966          1    8966.0    8966.0      8966      8966          0.0  PushPop  :task(2,3) 
      0.3             8964          1    8964.0    8964.0      8964      8964          0.0  PushPop  :task(31,7)
      0.3             8963          1    8963.0    8963.0      8963      8963          0.0  PushPop  :task(7,5) 
      0.3             8952          1    8952.0    8952.0      8952      8952          0.0  PushPop  :task(16,8)
      0.3             8946          1    8946.0    8946.0      8946      8946          0.0  PushPop  :task(4,2) 
      0.3             8934          1    8934.0    8934.0      8934      8934          0.0  PushPop  :task(10,3)
      0.3             8934          1    8934.0    8934.0      8934      8934          0.0  PushPop  :task(19,1)
      0.3             8905          1    8905.0    8905.0      8905      8905          0.0  PushPop  :task(1,2) 
      0.3             8904          1    8904.0    8904.0      8904      8904          0.0  PushPop  :task(12,1)
      0.3             8901          1    8901.0    8901.0      8901      8901          0.0  PushPop  :task(20,1)
      0.3             8899          1    8899.0    8899.0      8899      8899          0.0  PushPop  :task(3,2) 
      0.3             8886          1    8886.0    8886.0      8886      8886          0.0  PushPop  :task(14,2)
      0.3             8886          1    8886.0    8886.0      8886      8886          0.0  PushPop  :task(15,4)
      0.3             8884          1    8884.0    8884.0      8884      8884          0.0  PushPop  :task(10,2)
      0.3             8881          1    8881.0    8881.0      8881      8881          0.0  PushPop  :task(7,9) 
      0.3             8873          1    8873.0    8873.0      8873      8873          0.0  PushPop  :task(12,2)
      0.3             8868          1    8868.0    8868.0      8868      8868          0.0  PushPop  :task(22,7)
      0.3             8861          1    8861.0    8861.0      8861      8861          0.0  PushPop  :task(31,1)
      0.3             8850          1    8850.0    8850.0      8850      8850          0.0  PushPop  :task(6,5) 
      0.3             8840          1    8840.0    8840.0      8840      8840          0.0  PushPop  :task(27,2)
      0.3             8838          1    8838.0    8838.0      8838      8838          0.0  PushPop  :task(6,1) 
      0.3             8831          1    8831.0    8831.0      8831      8831          0.0  PushPop  :task(4,4) 
      0.3             8823          1    8823.0    8823.0      8823      8823          0.0  PushPop  :task(13,8)
      0.3             8823          1    8823.0    8823.0      8823      8823          0.0  PushPop  :task(25,2)
      0.3             8784          1    8784.0    8784.0      8784      8784          0.0  PushPop  :task(16,1)
      0.3             8780          1    8780.0    8780.0      8780      8780          0.0  PushPop  :task(19,4)
      0.3             8778          1    8778.0    8778.0      8778      8778          0.0  PushPop  :task(15,8)
      0.3             8775          1    8775.0    8775.0      8775      8775          0.0  PushPop  :task(28,2)
      0.3             8771          1    8771.0    8771.0      8771      8771          0.0  PushPop  :task(10,7)
      0.3             8768          1    8768.0    8768.0      8768      8768          0.0  PushPop  :task(27,5)
      0.3             8763          1    8763.0    8763.0      8763      8763          0.0  PushPop  :task(30,2)
      0.3             8762          1    8762.0    8762.0      8762      8762          0.0  PushPop  :task(16,9)
      0.3             8757          1    8757.0    8757.0      8757      8757          0.0  PushPop  :task(28,7)
      0.3             8755          1    8755.0    8755.0      8755      8755          0.0  PushPop  :task(21,5)
      0.3             8748          1    8748.0    8748.0      8748      8748          0.0  PushPop  :task(16,2)
      0.3             8745          1    8745.0    8745.0      8745      8745          0.0  PushPop  :task(20,5)
      0.3             8742          1    8742.0    8742.0      8742      8742          0.0  PushPop  :task(29,8)
      0.3             8741          1    8741.0    8741.0      8741      8741          0.0  PushPop  :task(28,4)
      0.3             8740          1    8740.0    8740.0      8740      8740          0.0  PushPop  :task(23,3)
      0.3             8740          1    8740.0    8740.0      8740      8740          0.0  PushPop  :task(26,4)
      0.3             8731          1    8731.0    8731.0      8731      8731          0.0  PushPop  :task(30,4)
      0.3             8730          1    8730.0    8730.0      8730      8730          0.0  PushPop  :task(24,8)
      0.3             8727          1    8727.0    8727.0      8727      8727          0.0  PushPop  :task(12,7)
      0.3             8724          1    8724.0    8724.0      8724      8724          0.0  PushPop  :task(4,7) 
      0.3             8720          1    8720.0    8720.0      8720      8720          0.0  PushPop  :task(28,8)
      0.3             8718          1    8718.0    8718.0      8718      8718          0.0  PushPop  :task(13,4)
      0.3             8706          1    8706.0    8706.0      8706      8706          0.0  PushPop  :task(29,5)
      0.3             8704          1    8704.0    8704.0      8704      8704          0.0  PushPop  :task(2,4) 
      0.3             8698          1    8698.0    8698.0      8698      8698          0.0  PushPop  :task(27,1)
      0.3             8672          1    8672.0    8672.0      8672      8672          0.0  PushPop  :task(24,4)
      0.3             8672          1    8672.0    8672.0      8672      8672          0.0  PushPop  :task(25,4)
      0.3             8669          1    8669.0    8669.0      8669      8669          0.0  PushPop  :task(25,7)
      0.3             8665          1    8665.0    8665.0      8665      8665          0.0  PushPop  :task(5,5) 
      0.3             8656          1    8656.0    8656.0      8656      8656          0.0  PushPop  :task(30,7)
      0.3             8651          1    8651.0    8651.0      8651      8651          0.0  PushPop  :task(3,4) 
      0.3             8649          1    8649.0    8649.0      8649      8649          0.0  PushPop  :task(4,6) 
      0.3             8644          1    8644.0    8644.0      8644      8644          0.0  PushPop  :task(11,2)
      0.3             8637          1    8637.0    8637.0      8637      8637          0.0  PushPop  :task(18,8)
      0.3             8628          1    8628.0    8628.0      8628      8628          0.0  PushPop  :task(24,2)
      0.3             8626          1    8626.0    8626.0      8626      8626          0.0  PushPop  :task(23,2)
      0.3             8625          1    8625.0    8625.0      8625      8625          0.0  PushPop  :task(17,2)
      0.3             8625          1    8625.0    8625.0      8625      8625          0.0  PushPop  :task(29,9)
      0.3             8607          1    8607.0    8607.0      8607      8607          0.0  PushPop  :task(4,9) 
      0.3             8604          1    8604.0    8604.0      8604      8604          0.0  PushPop  :task(14,3)
      0.3             8602          1    8602.0    8602.0      8602      8602          0.0  PushPop  :task(18,6)
      0.3             8601          1    8601.0    8601.0      8601      8601          0.0  PushPop  :task(29,6)
      0.3             8600          1    8600.0    8600.0      8600      8600          0.0  PushPop  :task(23,1)
      0.3             8597          1    8597.0    8597.0      8597      8597          0.0  PushPop  :task(23,5)
      0.3             8586          1    8586.0    8586.0      8586      8586          0.0  PushPop  :task(18,9)
      0.3             8583          1    8583.0    8583.0      8583      8583          0.0  PushPop  :task(27,3)
      0.3             8581          1    8581.0    8581.0      8581      8581          0.0  PushPop  :task(8,7) 
      0.3             8579          1    8579.0    8579.0      8579      8579          0.0  PushPop  :task(11,8)
      0.3             8565          1    8565.0    8565.0      8565      8565          0.0  PushPop  :task(16,4)
      0.3             8553          1    8553.0    8553.0      8553      8553          0.0  PushPop  :task(13,2)
      0.3             8553          1    8553.0    8553.0      8553      8553          0.0  PushPop  :task(13,7)
      0.3             8551          1    8551.0    8551.0      8551      8551          0.0  PushPop  :task(1,9) 
      0.3             8548          1    8548.0    8548.0      8548      8548          0.0  PushPop  :task(3,8) 
      0.3             8543          1    8543.0    8543.0      8543      8543          0.0  PushPop  :task(26,2)
      0.3             8543          1    8543.0    8543.0      8543      8543          0.0  PushPop  :task(8,3) 
      0.3             8542          1    8542.0    8542.0      8542      8542          0.0  PushPop  :task(31,5)
      0.3             8539          1    8539.0    8539.0      8539      8539          0.0  PushPop  :task(19,5)
      0.3             8535          1    8535.0    8535.0      8535      8535          0.0  PushPop  :task(17,7)
      0.3             8531          1    8531.0    8531.0      8531      8531          0.0  PushPop  :task(10,6)
      0.3             8531          1    8531.0    8531.0      8531      8531          0.0  PushPop  :task(8,1) 
      0.3             8529          1    8529.0    8529.0      8529      8529          0.0  PushPop  :task(19,7)
      0.3             8519          1    8519.0    8519.0      8519      8519          0.0  PushPop  :task(23,9)
      0.3             8518          1    8518.0    8518.0      8518      8518          0.0  PushPop  :task(26,8)
      0.3             8516          1    8516.0    8516.0      8516      8516          0.0  PushPop  :task(7,4) 
      0.3             8513          1    8513.0    8513.0      8513      8513          0.0  PushPop  :task(22,6)
      0.3             8510          1    8510.0    8510.0      8510      8510          0.0  PushPop  :task(25,5)
      0.3             8507          1    8507.0    8507.0      8507      8507          0.0  PushPop  :task(6,2) 
      0.3             8499          1    8499.0    8499.0      8499      8499          0.0  PushPop  :task(16,3)
      0.3             8499          1    8499.0    8499.0      8499      8499          0.0  PushPop  :task(20,4)
      0.3             8496          1    8496.0    8496.0      8496      8496          0.0  PushPop  :task(15,2)
      0.3             8491          1    8491.0    8491.0      8491      8491          0.0  PushPop  :task(18,4)
      0.3             8490          1    8490.0    8490.0      8490      8490          0.0  PushPop  :task(21,2)
      0.3             8487          1    8487.0    8487.0      8487      8487          0.0  PushPop  :task(21,1)
      0.3             8486          1    8486.0    8486.0      8486      8486          0.0  PushPop  :task(10,1)
      0.3             8486          1    8486.0    8486.0      8486      8486          0.0  PushPop  :task(11,6)
      0.3             8484          1    8484.0    8484.0      8484      8484          0.0  PushPop  :task(18,1)
      0.3             8484          1    8484.0    8484.0      8484      8484          0.0  PushPop  :task(5,8) 
      0.3             8483          1    8483.0    8483.0      8483      8483          0.0  PushPop  :task(30,3)
      0.3             8483          1    8483.0    8483.0      8483      8483          0.0  PushPop  :task(31,8)
      0.3             8481          1    8481.0    8481.0      8481      8481          0.0  PushPop  :task(26,3)
      0.3             8475          1    8475.0    8475.0      8475      8475          0.0  PushPop  :task(24,7)
      0.3             8474          1    8474.0    8474.0      8474      8474          0.0  PushPop  :task(28,3)
      0.3             8473          1    8473.0    8473.0      8473      8473          0.0  PushPop  :task(14,4)
      0.3             8472          1    8472.0    8472.0      8472      8472          0.0  PushPop  :task(28,6)
      0.3             8471          1    8471.0    8471.0      8471      8471          0.0  PushPop  :task(6,9) 
      0.3             8469          1    8469.0    8469.0      8469      8469          0.0  PushPop  :task(8,2) 
      0.3             8467          1    8467.0    8467.0      8467      8467          0.0  PushPop  :task(26,7)
      0.3             8460          1    8460.0    8460.0      8460      8460          0.0  PushPop  :task(22,4)
      0.3             8460          1    8460.0    8460.0      8460      8460          0.0  PushPop  :task(25,6)
      0.3             8457          1    8457.0    8457.0      8457      8457          0.0  PushPop  :task(6,7) 
      0.3             8450          1    8450.0    8450.0      8450      8450          0.0  PushPop  :task(20,8)
      0.3             8445          1    8445.0    8445.0      8445      8445          0.0  PushPop  :task(11,4)
      0.3             8445          1    8445.0    8445.0      8445      8445          0.0  PushPop  :task(27,9)
      0.3             8444          1    8444.0    8444.0      8444      8444          0.0  PushPop  :task(7,8) 
      0.3             8443          1    8443.0    8443.0      8443      8443          0.0  PushPop  :task(5,4) 
      0.3             8436          1    8436.0    8436.0      8436      8436          0.0  PushPop  :task(20,6)
      0.3             8433          1    8433.0    8433.0      8433      8433          0.0  PushPop  :task(12,5)
      0.3             8425          1    8425.0    8425.0      8425      8425          0.0  PushPop  :task(16,6)
      0.3             8416          1    8416.0    8416.0      8416      8416          0.0  PushPop  :task(20,7)
      0.3             8415          1    8415.0    8415.0      8415      8415          0.0  PushPop  :task(21,3)
      0.3             8413          1    8413.0    8413.0      8413      8413          0.0  PushPop  :task(12,3)
      0.3             8410          1    8410.0    8410.0      8410      8410          0.0  PushPop  :task(11,7)
      0.3             8408          1    8408.0    8408.0      8408      8408          0.0  PushPop  :task(2,9) 
      0.3             8400          1    8400.0    8400.0      8400      8400          0.0  PushPop  :task(9,4) 
      0.3             8390          1    8390.0    8390.0      8390      8390          0.0  PushPop  :task(14,5)
      0.3             8389          1    8389.0    8389.0      8389      8389          0.0  PushPop  :task(0,5) 
      0.3             8389          1    8389.0    8389.0      8389      8389          0.0  PushPop  :task(16,5)
      0.3             8385          1    8385.0    8385.0      8385      8385          0.0  PushPop  :task(25,8)
      0.3             8383          1    8383.0    8383.0      8383      8383          0.0  PushPop  :task(29,4)
      0.3             8382          1    8382.0    8382.0      8382      8382          0.0  PushPop  :task(14,8)
      0.3             8380          1    8380.0    8380.0      8380      8380          0.0  PushPop  :task(14,9)
      0.3             8377          1    8377.0    8377.0      8377      8377          0.0  PushPop  :task(27,4)
      0.3             8371          1    8371.0    8371.0      8371      8371          0.0  PushPop  :task(12,8)
      0.3             8346          1    8346.0    8346.0      8346      8346          0.0  PushPop  :task(11,3)
      0.3             8345          1    8345.0    8345.0      8345      8345          0.0  PushPop  :task(19,2)
      0.3             8341          1    8341.0    8341.0      8341      8341          0.0  PushPop  :task(25,9)
      0.3             8331          1    8331.0    8331.0      8331      8331          0.0  PushPop  :task(22,8)
      0.3             8323          1    8323.0    8323.0      8323      8323          0.0  PushPop  :task(22,2)
      0.3             8316          1    8316.0    8316.0      8316      8316          0.0  PushPop  :task(23,8)
      0.3             8315          1    8315.0    8315.0      8315      8315          0.0  PushPop  :task(30,6)
      0.3             8311          1    8311.0    8311.0      8311      8311          0.0  PushPop  :task(17,9)
      0.3             8309          1    8309.0    8309.0      8309      8309          0.0  PushPop  :task(27,6)
      0.3             8308          1    8308.0    8308.0      8308      8308          0.0  PushPop  :task(9,7) 
      0.3             8303          1    8303.0    8303.0      8303      8303          0.0  PushPop  :task(4,5) 
      0.3             8295          1    8295.0    8295.0      8295      8295          0.0  PushPop  :task(8,5) 
      0.3             8293          1    8293.0    8293.0      8293      8293          0.0  PushPop  :task(9,8) 
      0.3             8280          1    8280.0    8280.0      8280      8280          0.0  PushPop  :task(14,6)
      0.3             8275          1    8275.0    8275.0      8275      8275          0.0  PushPop  :task(23,6)
      0.3             8273          1    8273.0    8273.0      8273      8273          0.0  PushPop  :task(19,3)
      0.3             8273          1    8273.0    8273.0      8273      8273          0.0  PushPop  :task(27,8)
      0.3             8269          1    8269.0    8269.0      8269      8269          0.0  PushPop  :task(31,9)
      0.3             8267          1    8267.0    8267.0      8267      8267          0.0  PushPop  :task(6,3) 
      0.3             8265          1    8265.0    8265.0      8265      8265          0.0  PushPop  :task(6,6) 
      0.3             8261          1    8261.0    8261.0      8261      8261          0.0  PushPop  :task(17,5)
      0.3             8256          1    8256.0    8256.0      8256      8256          0.0  PushPop  :task(12,9)
      0.3             8256          1    8256.0    8256.0      8256      8256          0.0  PushPop  :task(30,5)
      0.3             8246          1    8246.0    8246.0      8246      8246          0.0  PushPop  :task(10,5)
      0.3             8244          1    8244.0    8244.0      8244      8244          0.0  PushPop  :task(31,6)
      0.3             8234          1    8234.0    8234.0      8234      8234          0.0  PushPop  :task(17,3)
      0.3             8223          1    8223.0    8223.0      8223      8223          0.0  PushPop  :task(28,5)
      0.3             8222          1    8222.0    8222.0      8222      8222          0.0  PushPop  :task(31,4)
      0.3             8221          1    8221.0    8221.0      8221      8221          0.0  PushPop  :task(17,6)
      0.3             8212          1    8212.0    8212.0      8212      8212          0.0  PushPop  :task(15,3)
      0.3             8207          1    8207.0    8207.0      8207      8207          0.0  PushPop  :task(8,9) 
      0.3             8206          1    8206.0    8206.0      8206      8206          0.0  PushPop  :task(19,6)
      0.3             8200          1    8200.0    8200.0      8200      8200          0.0  PushPop  :task(30,9)
      0.3             8196          1    8196.0    8196.0      8196      8196          0.0  PushPop  :task(13,3)
      0.3             8196          1    8196.0    8196.0      8196      8196          0.0  PushPop  :task(13,9)
      0.3             8196          1    8196.0    8196.0      8196      8196          0.0  PushPop  :task(4,3) 
      0.3             8189          1    8189.0    8189.0      8189      8189          0.0  PushPop  :task(19,9)
      0.3             8185          1    8185.0    8185.0      8185      8185          0.0  PushPop  :task(15,9)
      0.3             8182          1    8182.0    8182.0      8182      8182          0.0  PushPop  :task(1,6) 
      0.3             8181          1    8181.0    8181.0      8181      8181          0.0  PushPop  :task(21,9)
      0.3             8176          1    8176.0    8176.0      8176      8176          0.0  PushPop  :task(24,3)
      0.3             8168          1    8168.0    8168.0      8168      8168          0.0  PushPop  :task(3,9) 
      0.3             8165          1    8165.0    8165.0      8165      8165          0.0  PushPop  :task(1,8) 
      0.3             8165          1    8165.0    8165.0      8165      8165          0.0  PushPop  :task(12,6)
      0.3             8158          1    8158.0    8158.0      8158      8158          0.0  PushPop  :task(28,9)
      0.3             8150          1    8150.0    8150.0      8150      8150          0.0  PushPop  :task(26,9)
      0.3             8140          1    8140.0    8140.0      8140      8140          0.0  PushPop  :task(21,6)
      0.3             8132          1    8132.0    8132.0      8132      8132          0.0  PushPop  :task(2,2) 
      0.3             8024          1    8024.0    8024.0      8024      8024          0.0  PushPop  :task(3,5) 
      0.3             7940          1    7940.0    7940.0      7940      7940          0.0  PushPop  :task(2,7) 
      0.3             7709          1    7709.0    7709.0      7709      7709          0.0  PushPop  :task(1,5) 
      0.3             7438          1    7438.0    7438.0      7438      7438          0.0  PushPop  :task(23,0)
      0.3             7418          1    7418.0    7418.0      7418      7418          0.0  PushPop  :task(30,0)
      0.2             7280          1    7280.0    7280.0      7280      7280          0.0  PushPop  :task(0,3) 
      0.2             7278          1    7278.0    7278.0      7278      7278          0.0  PushPop  :task(1,7) 
      0.2             7231          1    7231.0    7231.0      7231      7231          0.0  PushPop  :task(0,2) 
      0.2             7194          1    7194.0    7194.0      7194      7194          0.0  PushPop  :task(7,0) 
      0.2             7134          1    7134.0    7134.0      7134      7134          0.0  PushPop  :task(20,0)
      0.2             6980          1    6980.0    6980.0      6980      6980          0.0  PushPop  :task(28,0)
      0.2             6972          1    6972.0    6972.0      6972      6972          0.0  PushPop  :task(12,0)
      0.2             6888          1    6888.0    6888.0      6888      6888          0.0  PushPop  :task(2,6) 
      0.2             6858          1    6858.0    6858.0      6858      6858          0.0  PushPop  :task(15,0)
      0.2             6611          1    6611.0    6611.0      6611      6611          0.0  PushPop  :task(11,0)
      0.2             6300          1    6300.0    6300.0      6300      6300          0.0  PushPop  :task(0,8) 
      0.2             6265          1    6265.0    6265.0      6265      6265          0.0  PushPop  :task(13,0)
      0.2             6213          1    6213.0    6213.0      6213      6213          0.0  PushPop  :task(0,4) 
      0.2             6128          1    6128.0    6128.0      6128      6128          0.0  PushPop  :task(5,0) 
      0.2             6119          1    6119.0    6119.0      6119      6119          0.0  PushPop  :task(0,6) 
      0.2             5968          1    5968.0    5968.0      5968      5968          0.0  PushPop  :task(0,7) 
      0.2             5777          1    5777.0    5777.0      5777      5777          0.0  PushPop  :task(4,0) 
      0.2             5664          1    5664.0    5664.0      5664      5664          0.0  PushPop  :task(10,0)
      0.2             5487          1    5487.0    5487.0      5487      5487          0.0  PushPop  :task(8,0) 
      0.2             5485          1    5485.0    5485.0      5485      5485          0.0  PushPop  :task(21,0)
      0.2             5434          1    5434.0    5434.0      5434      5434          0.0  PushPop  :task(31,0)
      0.2             5412          1    5412.0    5412.0      5412      5412          0.0  PushPop  :task(6,0) 
      0.2             5303          1    5303.0    5303.0      5303      5303          0.0  PushPop  :task(29,0)
      0.2             5266          1    5266.0    5266.0      5266      5266          0.0  PushPop  :task(18,0)
      0.2             5244          1    5244.0    5244.0      5244      5244          0.0  PushPop  :task(19,0)
      0.2             5177          1    5177.0    5177.0      5177      5177          0.0  PushPop  :task(14,0)
      0.2             5066          1    5066.0    5066.0      5066      5066          0.0  PushPop  :task(24,0)
      0.2             5049          1    5049.0    5049.0      5049      5049          0.0  PushPop  :task(9,0) 
      0.2             5037          1    5037.0    5037.0      5037      5037          0.0  PushPop  :task(16,0)
      0.2             5002          1    5002.0    5002.0      5002      5002          0.0  PushPop  :task(22,0)
      0.2             4994          1    4994.0    4994.0      4994      4994          0.0  PushPop  :task(17,0)
      0.2             4948          1    4948.0    4948.0      4948      4948          0.0  PushPop  :task(26,0)
      0.2             4927          1    4927.0    4927.0      4927      4927          0.0  PushPop  :task(27,0)

[4/8] Executing 'osrt_sum' stats report

 Time (%)  Total Time (ns)  Num Calls   Avg (ns)   Med (ns)   Min (ns)  Max (ns)   StdDev (ns)           Name         
 --------  ---------------  ---------  ----------  ---------  --------  ---------  -----------  ----------------------
     63.8        510846043         12  42570503.6  1636868.0      1021  407539081  116086116.3  poll                  
     35.5        284699566       2667    106749.0    12477.0      1003   24974639     730348.5  ioctl                 
      0.3          2395062         53     45189.8    18182.0      4381    1337574     181846.4  mmap64                
      0.1          1011411          3    337137.0   333973.0    316291     361147      22594.8  pthread_create        
      0.1           932347          9    103594.1    49821.0     24358     495035     149843.3  sem_timedwait         
      0.1           484517         85      5700.2     4979.0      2344      16900       2543.3  open64                
      0.0           387835         86      4509.7     2451.5      1033      28602       5311.3  fopen                 
      0.0           128544          1    128544.0   128544.0    128544     128544          0.0  pthread_cond_wait     
      0.0           127194         14      9085.3     4364.0      1811      51791      12897.7  mmap                  
      0.0            47677         21      2270.3     2062.0      1082       6825       1356.6  fclose                
      0.0            42050          1     42050.0    42050.0     42050      42050          0.0  fgets                 
      0.0            30800          6      5133.3     5986.5      1570       7698       2665.0  open                  
      0.0            25520         10      2552.0     2568.0      1818       3618        566.1  write                 
      0.0            20670         10      2067.0     1947.5      1084       4469        958.9  read                  
      0.0            19209          2      9604.5     9604.5      8034      11175       2221.0  socket                
      0.0            15151          3      5050.3     6185.0      2577       6389       2144.4  pipe2                 
      0.0            12092          3      4030.7     3675.0      2655       5762       1583.7  munmap                
      0.0             9259          1      9259.0     9259.0      9259       9259          0.0  connect               
      0.0             9005          1      9005.0     9005.0      9005       9005          0.0  fflush                
      0.0             4874          2      2437.0     2437.0      1526       3348       1288.3  fwrite                
      0.0             3700          1      3700.0     3700.0      3700       3700          0.0  fopen64               
      0.0             3243          2      1621.5     1621.5      1092       2151        748.8  pthread_cond_broadcast
      0.0             1967          1      1967.0     1967.0      1967       1967          0.0  bind                  
      0.0             1056          1      1056.0     1056.0      1056       1056          0.0  fcntl                 

[5/8] Executing 'cuda_api_sum' stats report

 Time (%)  Total Time (ns)  Num Calls   Avg (ns)     Med (ns)    Min (ns)   Max (ns)   StdDev (ns)                 Name               
 --------  ---------------  ---------  -----------  -----------  ---------  ---------  -----------  ----------------------------------
     79.6        139720287          1  139720287.0  139720287.0  139720287  139720287          0.0  cudaFree                          
     13.5         23707983        320      74087.4      74433.0      66441     167673       7420.7  cudaHostRegister                  
      5.9         10383284        320      32447.8      31587.0      29350     108504       4866.8  cudaHostUnregister                
      0.4           693920        320       2168.5       2028.0       1802      20961       1148.5  cudaEventRecord                   
      0.2           359190        549        654.3        637.0        478       6921        349.3  cudaStreamWaitEvent               
      0.1           256230        320        800.7        510.5        462      10231        927.1  cudaEventCreateWithFlags          
      0.1           164636        320        514.5        524.0        315       1394        112.2  cudaEventDestroy                  
      0.1            90696          1      90696.0      90696.0      90696      90696          0.0  cudaDeviceGetDefaultMemPool_v11020
 SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/cudastf_profile.sqlite does not contain CUDA kernel data.
SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/cudastf_profile.sqlite does not contain GPU memory data.
SKIPPED: /root/stf_exp/task-bench/cudastf_vs_openmp/cudastf_profile.sqlite does not contain GPU memory data.
     0.0            46132          4      11533.0       2970.5       2723      37468      17291.0  cudaStreamCreate                  
      0.0            15239          4       3809.8       2225.0       1897       8892       3400.9  cudaStreamDestroy                 
      0.0             2863          1       2863.0       2863.0       2863       2863          0.0  cudaStreamSynchronize             
      0.0             1147          1       1147.0       1147.0       1147       1147          0.0  cuModuleGetLoadingMode            

[6/8] Executing 'cuda_gpu_kern_sum' stats report
[7/8] Executing 'cuda_gpu_mem_time_sum' stats report
[8/8] Executing 'cuda_gpu_mem_size_sum' stats report
Generated:
	/root/stf_exp/task-bench/cudastf_vs_openmp/cudastf_profile.nsys-rep
	/root/stf_exp/task-bench/cudastf_vs_openmp/cudastf_profile.sqlite
